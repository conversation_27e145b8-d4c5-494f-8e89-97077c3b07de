# QA参考资料排序功能实现文档

## 概述

本文档描述了为QA和文本文件的参考资料添加排序功能的完整实现方案。该功能确保用户在创建和更新QA时设置的参考资料顺序能够被正确保存和显示。

## 问题分析

### 原有问题
1. **数据库层面**：参考资料表缺少排序字段
2. **存储层面**：参考资料按输入顺序存储，但没有保存顺序信息
3. **查询层面**：查询时没有按特定顺序排序
4. **显示层面**：前端显示的参考资料顺序可能与用户期望不一致

### 参考资料类型
- **引用其他文档**：存储在 `t_doc_reference` 表（多对多关系）
- **纯文本参考资料**：存储在 `t_doc_text_reference` 表（一对多关系）

## 解决方案

### 1. 数据库表结构改进

#### 添加排序字段
- `t_doc_reference` 表添加 `sort_order` 字段（INT，默认值0）
- `t_doc_text_reference` 表添加 `sort_order` 字段（INT，默认值0）

#### 创建索引
```sql
CREATE INDEX idx_doc_reference_sort ON t_doc_reference(doc_id, sort_order);
CREATE INDEX idx_doc_text_reference_sort ON t_doc_text_reference(doc_id, sort_order);
```

#### 迁移命令
创建了 `MigrateReferenceSortOrder()` 命令来自动添加字段和索引。

### 2. 模型结构更新

#### TDocReference 模型
```go
type TDocReference struct {
    DocID     int64 `gorm:"primaryKey;column:doc_id" json:"docId"`
    RefID     int64 `gorm:"primaryKey;column:ref_id" json:"refId"`
    SortOrder int32 `gorm:"column:sort_order;default:0" json:"sortOrder"` // 新增
}
```

#### TDocTextReference 模型
```go
type TDocTextReference struct {
    ID         uint64    `gorm:"primaryKey;column:id" json:"id"`
    DocID      uint64    `gorm:"column:doc_id" json:"docId"`
    Text       string    `gorm:"column:text" json:"text"`
    SortOrder  int32     `gorm:"column:sort_order;default:0" json:"sortOrder"` // 新增
    CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP" json:"createTime"`
}
```

### 3. 存储逻辑改进

#### 关联表字段设计
在 TDoc 模型中添加了专门的关联表字段：
```go
References     []*TDoc                 // 仅用于查询
DocReferences  []*TDocReference        // 引用文档参考资料关联表，用于创建
```

#### ReferenceFromProtoWithSort 方法
新增了 `ReferenceFromProtoWithSort` 方法，直接填充关联表数据：
```go
func (t *TDoc) ReferenceFromProtoWithSort(refs []*ai.DocReference) *TDoc {
    var docRefs []*TDocReference
    for i, v := range refs {
        sortOrder := int32(i) // 使用索引作为排序字段
        if v.Id != 0 {
            docRef := &TDocReference{
                DocID:     int64(t.ID),
                RefID:     int64(v.Id),
                SortOrder: sortOrder,
            }
            docRefs = append(docRefs, docRef)
        }
    }
    t.DocReferences = docRefs // 直接填充关联表数据
}
```

#### GORM自动创建
创建时使用GORM的自动关联创建功能：
```go
// 只排除References多对多关系，保留DocReferences和TextReferences
tx.Omit("References").Create(rows)
```

### 4. 查询逻辑改进

#### Preload 排序
修改了所有相关的 Preload 逻辑，确保查询时按排序字段排序：
```go
Preload("TextReferences", func(db *gorm.DB) *gorm.DB {
    return db.Order("sort_order ASC")
})
```

#### 涉及的方法
- `PreloadForListDocQueryQA`
- `PreloadForListDocQueryTextFile`
- `LoadDoc`
- `LoadDocWithTx`

### 5. 转换逻辑改进

#### ReferenceToProto 方法
完全重写了 `ReferenceToProto` 方法，确保按正确顺序返回参考资料：
```go
func (t *TDoc) ReferenceToProto() (refs []*ai.DocReference) {
    // 1. 查询引用文档的排序信息
    // 2. 合并所有参考资料并按 sort_order 排序
    // 3. 返回排序后的结果
}
```

### 6. 更新处理逻辑改进

#### QA更新
修改了 `UpdateQA` 中的参考资料更新逻辑：
```go
if (len(in.Reference) != 0 && in.Mask == nil) || slices.Contains(in.GetMask().GetPaths(), "reference") {
    err = row.ReplaceReferencesWithSort(tx, in.Reference)
    if err != nil {
        return err
    }
}
```

#### QA创建优化
修改了 `CreateQAInBulk` 中的创建逻辑，使用GORM自动创建：
```go
// 创建文档主体，包含关联表数据，GORM会自动创建关联记录
// 只排除References多对多关系，保留DocReferences和TextReferences
if err := tx.Omit("References").Create(rows).Error; err != nil {
    return err
}
```

#### FromQAProto 和 FromTextFileProto 方法
修改了这两个方法，使用新的 `ReferenceFromProtoWithSort` 方法：
```go
t.ReferenceFromProtoWithSort(p.Reference)
```

#### 关键优化点
1. **分离查询和创建**：`References` 仅用于查询，`DocReferences` 用于创建
2. **GORM自动处理**：利用GORM的关联创建功能，无需手动循环
3. **批量操作**：一次性创建所有关联记录，提高性能

#### 文本文件处理
同样修改了文本文件的创建和更新逻辑：
- `CreateTextFileInBulk`
- `UpdateTextFile`

## 部署说明

### 1. 数据库迁移
手工执行SQL迁移脚本：
```bash
# 执行 docs/reference_sort_migration.sql 中的SQL语句
mysql -u username -p database_name < docs/reference_sort_migration.sql
```

### 2. 代码部署
部署包含所有修改的新版本代码。

### 3. 验证
- 创建新的QA，验证参考资料顺序是否正确
- 更新现有QA的参考资料，验证顺序是否保持
- 查询QA详情，验证返回的参考资料顺序是否正确

## 兼容性说明

### 向后兼容
- 现有数据不受影响，新字段有默认值
- 现有API接口不变
- 现有功能正常工作

### 数据迁移
- 现有的参考资料会保持原有顺序（sort_order=0）
- 新创建或更新的参考资料会有正确的排序值

## 测试

创建了基础的单元测试文件 `t_doc_reference_test.go`，包含：
- `TestReferenceFromProto`：测试从Proto转换时的排序
- `TestReferenceToProto`：测试转换为Proto时的排序
- `TestReplaceReferencesWithSort`：测试替换功能

## 总结

该实现完整解决了QA参考资料排序的问题，确保：
1. 用户设置的参考资料顺序能够被正确保存
2. 查询时能够按正确顺序返回参考资料
3. 前端显示的顺序与用户期望一致
4. 向后兼容，不影响现有功能
