# 参考资料排序历史数据清理说明

## 概述

为了处理历史数据的排序问题，我们需要为现有的参考资料数据设置合理的 `sort_order` 值。

## 排序规则

根据您的要求，历史数据的排序规则如下：

1. **文本参考资料（t_doc_text_reference）**：按主键 `id` 排序，从 0 开始
2. **引用文档参考资料（t_doc_reference）**：排在文本参考资料之后，按 `ref_id` 排序

## 数据清理逻辑

### 示例场景

假设某个文档（doc_id=100）有以下参考资料：

**文本参考资料表（t_doc_text_reference）**：
- id=1001, doc_id=100, text="参考资料A"
- id=1003, doc_id=100, text="参考资料B"  
- id=1005, doc_id=100, text="参考资料C"

**引用文档参考资料表（t_doc_reference）**：
- doc_id=100, ref_id=200
- doc_id=100, ref_id=150

### 清理后的排序结果

**文本参考资料**（按 id 排序）：
- id=1001 → sort_order=0
- id=1003 → sort_order=1
- id=1005 → sort_order=2

**引用文档参考资料**（排在文本参考资料之后，按 ref_id 排序）：
- ref_id=150 → sort_order=3 (text_max_order + 0)
- ref_id=200 → sort_order=4 (text_max_order + 1)

### 最终显示顺序

用户看到的参考资料顺序将是：
1. 参考资料A (sort_order=0)
2. 参考资料B (sort_order=1) 
3. 参考资料C (sort_order=2)
4. 引用文档150 (sort_order=3)
5. 引用文档200 (sort_order=4)

## SQL执行步骤

### 步骤1：文本参考资料排序
```sql
UPDATE t_doc_text_reference t1
JOIN (
    SELECT 
        id,
        doc_id,
        ROW_NUMBER() OVER (PARTITION BY doc_id ORDER BY id) - 1 AS new_sort_order
    FROM t_doc_text_reference
    WHERE sort_order = 0
) t2 ON t1.id = t2.id
SET t1.sort_order = t2.new_sort_order;
```

**说明**：
- 使用 `ROW_NUMBER()` 为每个文档的文本参考资料按 `id` 排序
- 减1是因为排序从0开始
- 只更新 `sort_order = 0` 的记录（避免重复执行）

### 步骤2：引用文档参考资料排序
```sql
UPDATE t_doc_reference dr
JOIN (
    SELECT 
        doc_id,
        ref_id,
        ROW_NUMBER() OVER (PARTITION BY doc_id ORDER BY ref_id) - 1 AS ref_order
    FROM t_doc_reference
    WHERE sort_order = 0
) dr_ordered ON dr.doc_id = dr_ordered.doc_id AND dr.ref_id = dr_ordered.ref_id
LEFT JOIN (
    SELECT 
        doc_id,
        COALESCE(MAX(sort_order), -1) + 1 AS text_max_order
    FROM t_doc_text_reference
    GROUP BY doc_id
) tr_max ON dr.doc_id = tr_max.doc_id
SET dr.sort_order = COALESCE(tr_max.text_max_order, 0) + dr_ordered.ref_order;
```

**说明**：
- 先计算每个文档的文本参考资料的最大排序值
- 引用文档参考资料从 `text_max_order + 1` 开始排序
- 按 `ref_id` 排序确保一致性

## 验证查询

### 检查排序结果
```sql
SELECT 
    doc_id,
    'text_reference' as type,
    COUNT(*) as count,
    MIN(sort_order) as min_order,
    MAX(sort_order) as max_order
FROM t_doc_text_reference 
GROUP BY doc_id
UNION ALL
SELECT 
    doc_id,
    'doc_reference' as type,
    COUNT(*) as count,
    MIN(sort_order) as min_order,
    MAX(sort_order) as max_order
FROM t_doc_reference 
GROUP BY doc_id
ORDER BY doc_id, type;
```

### 检查未处理的记录
```sql
SELECT 'text_reference' as table_name, COUNT(*) as zero_sort_count
FROM t_doc_text_reference WHERE sort_order = 0
UNION ALL
SELECT 'doc_reference' as table_name, COUNT(*) as zero_sort_count
FROM t_doc_reference WHERE sort_order = 0;
```

## 注意事项

1. **执行前备份**：务必在执行前备份相关表数据
2. **分批执行**：如果数据量很大，可以考虑分批执行
3. **验证结果**：执行后务必验证排序结果是否符合预期
4. **幂等性**：SQL设计为幂等，可以安全重复执行

## 回滚方案

如果需要回滚，可以执行：
```sql
UPDATE t_doc_text_reference SET sort_order = 0;
UPDATE t_doc_reference SET sort_order = 0;
```
