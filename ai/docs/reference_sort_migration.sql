-- QA参考资料排序功能数据库迁移SQL
-- 执行前请备份相关表数据

-- 1. 为 t_doc_reference 表添加 sort_order 字段
ALTER TABLE t_doc_reference ADD COLUMN sort_order INT DEFAULT 0;

-- 2. 为 t_doc_text_reference 表添加 sort_order 字段  
ALTER TABLE t_doc_text_reference ADD COLUMN sort_order INT DEFAULT 0;


-- 4. 历史数据清理：为现有数据设置排序
-- 4.1 更新 t_doc_text_reference 表的 sort_order，按主键 id 排序
UPDATE t_doc_text_reference t1
JOIN (
    SELECT 
        id,
        doc_id,
        ROW_NUMBER() OVER (PARTITION BY doc_id ORDER BY id) - 1 AS new_sort_order
    FROM t_doc_text_reference
    WHERE sort_order = 0
) t2 ON t1.id = t2.id
SET t1.sort_order = t2.new_sort_order;

-- 4.2 更新 t_doc_reference 表的 sort_order，排在 text reference 之后
UPDATE t_doc_reference dr
JOIN (
    SELECT 
        doc_id,
        ref_id,
        ROW_NUMBER() OVER (PARTITION BY doc_id ORDER BY ref_id) - 1 AS ref_order
    FROM t_doc_reference
    WHERE sort_order = 0
) dr_ordered ON dr.doc_id = dr_ordered.doc_id AND dr.ref_id = dr_ordered.ref_id
LEFT JOIN (
    SELECT 
        doc_id,
        COALESCE(MAX(sort_order), -1) + 1 AS text_max_order
    FROM t_doc_text_reference
    GROUP BY doc_id
) tr_max ON dr.doc_id = tr_max.doc_id
SET dr.sort_order = COALESCE(tr_max.text_max_order, 0) + dr_ordered.ref_order;