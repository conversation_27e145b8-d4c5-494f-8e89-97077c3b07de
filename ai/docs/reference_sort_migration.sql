-- QA参考资料排序功能数据库迁移SQL
-- 执行前请备份相关表数据

-- 1. 为 t_doc_reference 表添加 sort_order 字段
ALTER TABLE t_doc_reference ADD COLUMN sort_order INT DEFAULT 0;

-- 2. 为 t_doc_text_reference 表添加 sort_order 字段  
ALTER TABLE t_doc_text_reference ADD COLUMN sort_order INT DEFAULT 0;

-- 3. 创建索引优化查询性能
CREATE INDEX idx_doc_reference_sort ON t_doc_reference(doc_id, sort_order);
CREATE INDEX idx_doc_text_reference_sort ON t_doc_text_reference(doc_id, sort_order);

-- 4. 验证字段添加成功
SELECT 
    table_name, 
    column_name, 
    data_type, 
    column_default
FROM information_schema.columns 
WHERE table_name IN ('t_doc_reference', 't_doc_text_reference') 
    AND column_name = 'sort_order';

-- 5. 验证索引创建成功
SHOW INDEX FROM t_doc_reference WHERE Key_name = 'idx_doc_reference_sort';
SHOW INDEX FROM t_doc_text_reference WHERE Key_name = 'idx_doc_text_reference_sort';
