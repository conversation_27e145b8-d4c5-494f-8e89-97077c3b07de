// Package cmd ...
package cmd

import (
	"context"
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	golog "log"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"golang.org/x/exp/slices"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// RegisterCmd 注册命令
func RegisterCmd() error {
	ReparseFailedDoc()
	MigrateDoc()
	migrateAssistant()
	decodeHashids()
	encodeHashids()
	GenDocFileSizeToDB()
	MigrateDocSync()
	CloneDocToAssistant()
	CloneDocToCollection()
	ReparseDoc()
	NormalizeQAHash()
	createCollection()
	mergeLines()
	updateChunks()
	migrateAllDoc()
	migrateAllFailedDoc()
	migrateAllIncrementalDoc()
	ExtractDocTable()
	MigrateTextReferences()
	VerifyMigratedTextReferences()
	MigrateReferenceSortOrder()
	MigrateTencentDocToken()
	RefreshTencentDocUserInfo()
	return nil
}

// SyncDocToRag 同步collection到rag侧数据库
func SyncDocToRag() error {
	ok := config.GetBoolOr("llm.collection.sync_enabled", false)
	if ok {
		ctx := context.TODO()
		manager := logic.GetDocSyncRagManager(ctx, logic.ConfigDocSyncQueueStrategyLoader)
		parallelism := config.GetIntOr("llm.collection.sync_parallelism", 1)
		for i := 0; i < parallelism; i++ {
			i := i
			// 遇到错误继续往前执行
			xsync.SafeGo(ctx, func(ctx context.Context) error {
				manager.Do(i)
				return nil
			}, boot.TraceGo(ctx))
		}
		// 专门处理失败的记录
		xsync.SafeGo(ctx, func(ctx context.Context) error {
			manager.DoFailed()
			return nil
		}, boot.TraceGo(ctx))
		// 启动主动监控协程
		xsync.SafeGo(ctx, func(ctx context.Context) error {
			manager.MonitorPendingSync()
			return nil
		}, boot.TraceGo(ctx))
	}
	return nil
}

// StopSyncDocToRag 停止同步，删除锁资源
func StopSyncDocToRag() error {
	ok := config.GetBoolOr("llm.collection.sync_enabled", false)
	if ok {
		ctx := context.TODO()
		manager := logic.GetDocSyncRagManager(ctx)
		manager.ReleaseAllLock()
	}
	return nil
}

// ReparseFailedDoc 重新解析解析失败的文件
func ReparseFailedDoc() {
	if !config.GetBool("tanlive.cmd.REPARSEFAILEDDOC") {
		return
	}
	defer os.Exit(0)

	ctx := context.TODO()
	limit := 1
	latestId := uint64(0)
	for {
		var rows []*model.TDoc
		err := model.NewQuery[model.TDoc](ctx).Where("state = ? and id > ?", ai.DocState_DOC_STATE_PARES_FAILED, latestId).
			Limit(limit).OrderBy("id", false).Scan(&rows)
		if err != nil {
			fmt.Printf("reparse file doc err: %s\n", err.Error())
			time.Sleep(time.Second * 10)
		}
		if len(rows) == 0 {
			break
		}
		row := rows[0]
		fmt.Printf("reparsing failed docs id: %v\n", row.ID)
		l := logic.NewDocFileManager(ctx, row)
		err = l.ParseDocToDB(logic.ParseDocParam{
			Force:     true,
			IgnoreErr: true,
		})
		l.Clean()
		if err != nil {
			fmt.Printf("failed reparse file docs id: %v, err: %s\n", row.ID, err.Error())
		} else {
			fmt.Printf("succeed reparse file docs id: %v\n", row.ID)
		}
		latestId = row.ID
	}
	fmt.Println("reparse failed doc done.")
}

// CloneDocToAssistant 把一个助手知识复制到另外一个助手
// 只复制启用禁用的
// 不能复制增量数据
func CloneDocToAssistant() {
	if !config.GetBool("tanlive.cmd.CLONEDOCTOASSISTANT") {
		return
	}
	defer os.Exit(0)
	ctx := context.TODO()

	fromStr := config.GetString("tanlive.cmd.CLONEDOCTOASSISTANT.FROM")
	if fromStr == "" {
		panic("invalid cloned doc from")
	}
	toStr := config.GetString("tanlive.cmd.CLONEDOCTOASSISTANT.TO")
	if toStr == "" {
		panic("invalid cloned doc to")
	}

	var fromIds []uint64
	for _, from := range strings.Split(fromStr, ",") {
		fromId, err := strconv.ParseUint(from, 10, 64)
		if err != nil {
			panic("invalid cloned doc from")
		}
		fromIds = append(fromIds, fromId)
	}
	if len(fromIds) == 0 {
		panic("invalid cloned doc from")
	}

	toId, err := strconv.ParseUint(toStr, 10, 64)
	if err != nil {
		panic("invalid cloned doc to")
	}
	latestId := 0
	lastIdStr := config.GetString("tanlive.cmd.CLONEDOCTOASSISTANT.LASTDOCID")
	if len(lastIdStr) != 0 {
		latestId, _ = strconv.Atoi(lastIdStr)
	}
	maxId := 0
	maxIdStr := config.GetString("tanlive.cmd.CLONEDOCTOASSISTANT.MAXDOCID")
	if len(maxIdStr) != 0 {
		maxId, _ = strconv.Atoi(maxIdStr)
	}

	// -1 代表一直尝试
	retry := config.GetIntOr("tanlive.cmd.CLONEDOCTOASSISTANT.RETRY", -1)

	insertAssistantDocMapping := config.GetBoolOr("tanlive.cmd.CLONEDOCTOASSISTANT.INSERTMAPPING", false)

	// 如果再目标助手已经存在，是否跳过
	skipExisted := config.GetBoolOr("tanlive.cmd.CLONEDOCTOASSISTANT.SKIPEXISTED", false)
	scopeSkipExisted := func(db *gorm.DB) *gorm.DB {
		if skipExisted {
			subQ := model.NewQuery[model.TAssistantDoc](ctx).DB().
				Table("t_assistant_doc t1").
				Select("1").
				Where("t1.assistant_id = ?", toId).
				Where("t1.doc_id = t_assistant_doc.doc_id")
			return db.Where("NOT EXISTS (?)", subQ)
		}
		return db
	}

	for {
		docExistQ := model.NewQuery[model.TDoc](ctx).Select("1").
			Where("id = t_assistant_doc.doc_id").DB()
		remain, err := model.NewQuery[model.TAssistantDoc](ctx).
			Where("doc_id > ?", latestId).
			Where("assistant_id in ?", fromIds).
			Where("state = ? or state = ?", 1, 2).
			Scope(func(db *gorm.DB) *gorm.DB {
				if maxId != 0 {
					return db.Where("doc_id <= ?", maxId)
				}
				return db
			}).
			Where("EXISTS (?)", docExistQ).
			Scope(scopeSkipExisted).
			Count()
		if err != nil {
			golog.Println("Get remain doc count err:", err.Error(), "retry...")
			continue
		}
		golog.Printf("还剩余 %d 个文档待同步\n", remain)
		if remain == 0 {
			return
		}
		mp := &model.TAssistantDoc{}
		err = model.NewQuery[model.TAssistantDoc](ctx).
			Where("doc_id > ?", latestId).
			Where("assistant_id in ?", fromIds).
			Where("state = ? or state = ?", 1, 2).
			OrderBy("doc_id", false).
			Scope(scopeSkipExisted).
			Where("EXISTS (?)", docExistQ).
			Limit(1).DB().
			Find(&mp).Error
		if err != nil {
			golog.Println("Get remain doc count err:", err.Error(), "retry...")
			continue
		}

		id := mp.DocID
		var doc *model.TDoc

		err = model.NewQuery[model.TDoc](ctx).Where("id = ?", id).DB().
			Preload("Assistants", model.AssistantWithCollection, func(db *gorm.DB) *gorm.DB {
				return db.Where("id in ?", fromIds)
			}).
			Preload("MatchPatterns").
			Preload("States").First(&doc).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			latestId = int(id)
			continue
		}
		if err != nil {
			golog.Printf("err occured: %s\n, retry...", err.Error())
			continue
		}
		var toAssistant *model.TAssistant
		err = model.NewQuery[model.TAssistant](ctx).Where("id = ?", toId).
			DB().Scopes(model.AssistantWithCollection).Find(&toAssistant).Error
		if err != nil {
			golog.Printf("err occured: %s\n, retry...", err.Error())
			continue
		}

		doc.Assistants = append(doc.Assistants, toAssistant)
		// 改为目标助手
		for _, v := range doc.States {
			v.AssistantID = toId
		}
		if len(doc.Assistants) != 0 {
			golog.Println("Start migrate doc:", doc.ID)
			async := config.GetBoolOr("tanlive.cmd.CLONEDOCTOASSISTANT.ASYNC", false)

			for retried := 1; retry < 0 || retried <= retry; retried++ {
				if async {
					err = model.Transaction(ctx, func(tx *gorm.DB) error {
						return logic.NewDocSyncLogic(ctx, doc, nil).SyncToDBLog(tx)
					})
				} else {
					logic.RemoveDocDisabledCollection(doc)
					logEntry := logic.GenSyncLogEntry(ctx, doc.ID, nil, doc)
					for _, entry := range logEntry {
						err = logic.NewDocSyncLogic(ctx, entry.New, nil).Sync()
					}
				}

				if err != nil {
					// 重试次数耗完
					if retry == retried {
						golog.Println("!!!同步失败，docId: ", doc.ID)
						break
					}
					golog.Printf("docId: %d, err occured: %s\n, retry(%d/%d)", doc.ID, err.Error(), retried, retry)
					continue
				}

				// 建立doc和assistant的关联
				if insertAssistantDocMapping {
					err = model.NewQuery[model.TAssistantDoc](ctx).Clause(clause.OnConflict{
						UpdateAll: true,
					}).Create(&model.TAssistantDoc{
						DocID:       mp.DocID,
						AssistantID: toId,
						State:       mp.State,
						IsShared:    mp.IsShared,
					})
					if err != nil {
						golog.Println("docId: ", mp.DocID, "insertAssistantDocMapping err:", err.Error(), "retry...")
					}
				}
				golog.Println("Success migrate doc:", doc.ID)
				break
			}
		}
		latestId = int(doc.ID)
	}
}

// CloneDocToCollection 把一个助手的知识，复制到另外一个Collection
// 较为底层的用法，可以在未创建助手时先写入 collection
// 支持迁移过程的增量数据
func CloneDocToCollection() {
	if !config.GetBool("tanlive.cmd.CLONEDOCTOCOLLECTION") {
		return
	}
	defer os.Exit(0)
	ctx := context.TODO()

	fromId := config.GetUint64("tanlive.cmd.CLONEDOCTOCOLLECTION.FROM")
	toId := config.GetUint64("tanlive.cmd.CLONEDOCTOCOLLECTION.TO")

	if fromId == 0 || toId == 0 {
		golog.Fatalf("参数不正确，请指定助手和 collection")
	}
	latestId := config.GetUint64Or("tanlive.cmd.CLONEDOCTOCOLLECTION.LASTDOCID", 0)

	// -1 代表一直尝试
	retry := config.GetIntOr("tanlive.cmd.CLONEDOCTOCOLLECTION.RETRY", -1)

	var collection *model.TCollection
	err := model.NewQuery[model.TCollection](ctx).Where("id = ?", toId).DB().First(&collection).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		golog.Fatalf("目标 collection: %d 不存在", toId)
	}

	// 检查是否开启存量数据迁移
	migrateExistingData := config.GetBoolOr("tanlive.cmd.CLONEDOCTOCOLLECTION.MIGRATE_EXISTING", true)
	// 检查是否开启增量数据迁移
	migrateIncrementalData := config.GetBoolOr("tanlive.cmd.CLONEDOCTOCOLLECTION.MIGRATE_INCREMENTAL", true)

	maxSyncIdSinceStart := config.GetIntOr("tanlive.cmd.CLONEDOCTOCOLLECTION.MIGRATE_INCREMENTAL_START", -1)
	if maxSyncIdSinceStart == -1 {
		err = model.NewQuery[model.TDocSync](ctx).Select("IFNULL(MAX(id), 0)").Scan(&maxSyncIdSinceStart)
		if err != nil {
			golog.Fatalf("获取增量数据开始日志ID时发生错误: %s，退出", err.Error())
		}
	}
	golog.Println("增量数据开始日志ID：", maxSyncIdSinceStart)

	docTypeStr := config.GetString("tanlive.cmd.CLONEDOCTOCOLLECTION.DOCTYPE")
	var docType []int
	if len(docTypeStr) > 0 {
		for _, v := range strings.Split(docTypeStr, ",") {
			i, err := strconv.Atoi(v)
			if err != nil {
				golog.Fatalf("未知的doctype: %s", v)
			}
			docType = append(docType, i)
		}
		golog.Println("指定了文档类型：", docType)
	}

	// 是否开启 mergeline
	mergeLine := config.GetBoolOr("tanlive.cmd.CLONEDOCTOCOLLECTION.MERGELINE", false)

	// 获取并发数配置
	concurrency := config.GetIntOr("tanlive.cmd.CLONEDOCTOCOLLECTION.CONCURRENCY", 1)
	if concurrency <= 0 {
		concurrency = 1
	}
	golog.Printf("使用 %d 个协程进行并发处理\n", concurrency)

	// 存量数据迁移
	if migrateExistingData {
		// 使用原子计数器统计处理进度
		var processedCount int64
		var totalProcessingTime int64

		findDoc := func(ctx context.Context, docID uint64, docType []int) (*model.TDoc, error) {
			var doc *model.TDoc
			db := model.NewQuery[model.TDoc](ctx).Where("id = ?", docID).DB().
				Preload("MatchPatterns")
			if len(docType) != 0 {
				db = db.Where("data_type in ?", docType)
			}
			if err := db.First(&doc).Error; err != nil {
				return nil, fmt.Errorf("获取文档时发生错误: %s", err.Error())
			}
			return doc, nil
		}

		// 使用 worker pool 模式处理
		docChan := make(chan uint64, concurrency*2)
		g, gCtx := errgroup.WithContext(ctx)

		// 启动 worker goroutines
		for i := 0; i < concurrency; i++ {
			g.Go(func() error {
				for {
					select {
					case docID, ok := <-docChan:
						if !ok {
							return nil // channel 关闭，退出
						}
						doc, err := findDoc(ctx, docID, docType)
						if err != nil {
							golog.Printf("处理文档失败: %s\n", err.Error())
						} else {
							processingTime, count, err := SyncDoc(ctx, doc, collection, mergeLine, retry, false)
							// 统计
							atomic.AddInt64(&totalProcessingTime, processingTime)
							atomic.AddInt64(&processedCount, count)
							// 继续处理其他文档，不返回错误
							if err != nil {
								golog.Printf("处理文档失败: %s\n", err.Error())
							}
						}
					case <-gCtx.Done():
						return gCtx.Err()
					}
				}
			})
		}

		// 生产者：获取文档ID并发送到channel
		g.Go(func() error {
			defer close(docChan)
			batchSize := 100
			currentLatestId := latestId

			for {
				docExistQ := model.NewQuery[model.TDoc](ctx).Select("1").
					Where("id = t_assistant_doc.doc_id").DB()

				// 检查剩余文档数量
				remain, err := model.NewQuery[model.TAssistantDoc](ctx).
					Where("doc_id > ?", currentLatestId).
					Where("assistant_id = ?", fromId).
					Where("state = ?", 1).
					Where("EXISTS (?)", docExistQ).
					Count()
				if err != nil {
					return fmt.Errorf("获取剩余文档数量时发生错误: %s", err.Error())
				}

				// 计算平均处理时间和预计剩余时间
				var estimatedTimeStr string
				processed := atomic.LoadInt64(&processedCount)
				totalTime := atomic.LoadInt64(&totalProcessingTime)
				if processed > 0 {
					avgProcessTime := time.Duration(totalTime) / time.Duration(processed)
					estimatedTime := time.Duration(remain) * avgProcessTime
					estimatedTimeStr = fmt.Sprintf("，预计剩余时间：%v", estimatedTime.Round(time.Second))
				}
				golog.Printf("还剩余 %d 个文档待同步%s\n", remain, estimatedTimeStr)

				if remain == 0 {
					break
				}

				// 批量获取文档ID
				var docIDs []uint64
				err = model.NewQuery[model.TAssistantDoc](ctx).
					Where("doc_id > ?", currentLatestId).
					Where("assistant_id = ?", fromId).
					Where("state = ?", 1).
					Where("EXISTS (?)", docExistQ).
					OrderBy("doc_id", false).
					Limit(batchSize).
					Pluck("doc_id", &docIDs)
				if err != nil {
					return fmt.Errorf("获取文档ID时发生错误: %s", err.Error())
				}

				if len(docIDs) == 0 {
					break
				}

				// 发送文档ID到处理队列
				for _, docID := range docIDs {
					select {
					case docChan <- docID:
					case <-gCtx.Done():
						return gCtx.Err()
					}
				}

				currentLatestId = docIDs[len(docIDs)-1]

				// 短暂休眠，避免生产者跑得太快
				time.Sleep(1000 * time.Millisecond)
			}
			return nil
		})

		// 等待所有 worker 完成
		if err := g.Wait(); err != nil {
			golog.Printf("并发处理出错: %s\n", err.Error())
		}

		golog.Println("全量数据同步完成")
	}

	// 增量数据迁移
	if !migrateIncrementalData {
		golog.Println("增量数据迁移未开启，跳过")
		golog.Println("迁移完成")
		return
	}

	oldAssistant, err := model.NewQuery[model.TAssistantCollection](ctx).Where("assistant_id = ?", fromId).Find()
	if err != nil {
		return
	}
	for {
		redoLogs, err := model.NewQuery[model.TDocSync](ctx).
			OrderBy("id", false).
			Limit(50).
			GetBy("id > ? and collection_id = ?", maxSyncIdSinceStart, oldAssistant.CollectionID)
		if err != nil {
			golog.Fatalf("迁移增量数据出错，请手工确认，maxSyncId = %d: %s", maxSyncIdSinceStart, err.Error())
		}
		if len(redoLogs) == 0 {
			golog.Println("增量数据同步完成")
			break
		}
		maxSyncIdSinceStart = int(redoLogs[len(redoLogs)-1].ID)
		for _, v := range redoLogs {
			var doc *model.TDoc
			v.CollectionID = toId
			if v.Old == nil && v.New == nil {
				continue
			}
			// 插入
			if v.Old != nil {
				doc = v.Old
				v.Old.Assistants = nil
				v.Old.States = nil
				v.Old.Collections = []*model.TCollection{collection}
			}
			// 删除
			if v.New != nil {
				doc = v.New
				v.New.Assistants = nil
				v.New.States = nil
				v.New.Collections = []*model.TCollection{collection}

				getDoc, err := model.NewQuery[model.TDoc](ctx).Select("data_type").Where("id = ?", v.DocID).Find()
				if err != nil {
					golog.Printf("迁移增量数据出错，请手工确认，maxSyncId = %d: %s", maxSyncIdSinceStart, err.Error())
					continue
				}
				if getDoc.DataType != uint32(ai.DocType_DOCTYPE_QA) && mergeLine {
					v.New.IndexText = rag.MergeLines(v.New.IndexText)
				}
			}
			if doc == nil {
				continue
			}
			for retried := 1; retry < 0 || retried <= retry; retried++ {
				err = logic.NewDocSyncLogic(ctx, v.New, v.Old).Sync()
				if err != nil {
					// 重试次数耗完
					if retry == retried {
						golog.Printf("!!!增量数据同步失败，日志ID: %d，已重试 %d 次\n", v.ID, retried)
						break
					}
					golog.Printf("增量数据同步日志ID: %d，发生错误: %s，正在重试(%d/%d)\n", v.ID, err.Error(), retried, retry)
					continue
				}
				retry = config.GetIntOr("tanlive.cmd.CLONEDOCTOCOLLECTION.RETRY", -1)
				golog.Println("增量数据同步成功，日志ID: ", v.ID)
				break
			}
		}
	}
	golog.Println("迁移完成")
}

// SyncDoc 同步文档到collection
func SyncDoc(ctx context.Context, doc *model.TDoc, collection *model.TCollection, mergeLine bool, retry int, isDelete bool) (processingTime, processedCount int64, err error) {
	var (
		startTime = time.Now()
		fname     = doc.FileName
	)
	defer func() {
		d := time.Since(startTime)
		processingTime = int64(d)
		golog.Printf("文档迁移成功：%s(%d)@%s，耗时：%d\n", fname, doc.ID, collection.RagName, d.Round(time.Millisecond))
	}()

	doc.Assistants = nil
	doc.States = nil
	doc.Collections = []*model.TCollection{collection}

	if doc.DataType != uint32(ai.DocType_DOCTYPE_QA) && mergeLine {
		doc.IndexText = rag.MergeLines(doc.IndexText)
	}
	if doc.DataType == uint32(ai.DocType_DOCTYPE_QA) {
		fname = doc.IndexText
	}
	golog.Printf("开始迁移文档：%s(%d)@%s\n", fname, doc.ID, collection.RagName)

	var logEntry *model.TDocSync
	if isDelete {
		logEntry = logic.GenSyncLogEntry(ctx, doc.ID, doc, nil)[0]
	} else {
		logEntry = logic.GenSyncLogEntry(ctx, doc.ID, nil, doc)[0]
	}

	for retried := 1; retry < 0 || retried <= retry; retried++ {
		err = logic.NewDocSyncLogic(ctx, logEntry.New, logEntry.Old).Sync()
		if err != nil {
			// 重试次数耗完
			if retry == retried {
				err = fmt.Errorf("!!!文档同步失败：%s(%d)@%s，已重试%d次，失败原因：%w", fname, doc.ID, collection.RagName, retried, err)
				return
			}
			golog.Printf("文档同步失败：%s(%d)@%s，发生错误: %s，正在重试(%d/%d)\n", fname, doc.ID, collection.RagName, err.Error(), retried, retry)
			continue
		}
		processedCount += 1
		break
	}
	return
}

// MigrateDocSync  环境迁移重放 doc,同步执行
func MigrateDocSync() {
	if !config.GetBool("tanlive.cmd.MIGRATEDOCSYNC") {
		return
	}
	defer os.Exit(0)
	ctx := context.TODO()

	str := config.GetString("tanlive.cmd.MIGRATEASSISTANTID")
	if len(str) == 0 {
		return
	}
	assistant := make([]int, 0)
	ss := strings.Split(str, ",")
	for _, v := range ss {
		i, err := strconv.Atoi(v)
		if err != nil {
			panic(err)
		}
		assistant = append(assistant, i)
	}

	latestId := 0
	lastIdStr := config.GetString("tanlive.cmd.MIGRATEDOCSYNC_LASTDOCID")
	if len(lastIdStr) != 0 {
		latestId, _ = strconv.Atoi(lastIdStr)
	}

	for {
		remain, err := model.NewQuery[model.TAssistantDoc](ctx).
			Where("doc_id > ?", latestId).
			Where("assistant_id in (?)", assistant).
			Where("state = ?", 1).
			Count()
		if err != nil {
			golog.Println("Get remain doc count err:", err.Error(), "retry...")
			continue
		}
		golog.Printf("还剩余 %d 个文档待同步\n", remain)
		if remain == 0 {
			return
		}
		id := 0
		err = model.NewQuery[model.TAssistantDoc](ctx).
			Where("doc_id > ?", latestId).
			Where("assistant_id in (?)", assistant).
			Where("state = ?", 1).
			OrderBy("doc_id", false).
			Limit(1).
			Pluck("doc_id", &id)
		if err != nil {
			golog.Println("Get remain doc count err:", err.Error(), "retry...")
			continue
		}

		var doc *model.TDoc
		err = model.NewQuery[model.TDoc](ctx).Where("id = ?", id).DB().
			Preload("Assistants", model.AssistantWithCollection, func(db *gorm.DB) *gorm.DB {
				return db.Where("id in ?", assistant)
			}).
			Preload("MatchPatterns").
			Preload("States").First(&doc).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			continue
		}
		if err != nil {
			golog.Printf("err occured: %s\n, retry...", err.Error())
			continue
		}
		bk := doc.Assistants
		doc.Assistants = nil
		for _, v := range bk {
			if slices.Contains(assistant, int(v.ID)) {
				doc.Assistants = append(doc.Assistants, v)
			}
		}
		if len(doc.Assistants) != 0 {
			golog.Println("Start migrate doc:", doc.ID)
			logic.RemoveDocDisabledCollection(doc)
			logEntry := logic.GenSyncLogEntry(ctx, doc.ID, nil, doc)
			for _, entry := range logEntry {
				err = logic.NewDocSyncLogic(ctx, entry.New, nil).Sync()
				if err != nil {
					golog.Printf("err occured: %s\n, retry...", err.Error())
					continue
				}
			}
			golog.Println("Success migrate doc:", doc.ID)
		}
		latestId = int(doc.ID)
	}
}

// MigrateDoc 环境迁移重放 doc
func MigrateDoc() {
	if !config.GetBool("tanlive.cmd.MIGRATE") {
		return
	}
	defer os.Exit(0)
	ctx := context.TODO()

	str := config.GetString("tanlive.cmd.MIGRATEASSISTANTID")
	if len(str) == 0 {
		return
	}
	assistant := make([]int, 0)
	ss := strings.Split(str, ",")
	for _, v := range ss {
		i, err := strconv.Atoi(v)
		if err != nil {
			panic(err)
		}
		assistant = append(assistant, i)
	}

	latestId := uint64(0)
	for {
		var doc *model.TDoc
		err := model.NewQuery[model.TDoc](ctx).Where("id > ?", latestId).DB().
			Preload("Assistants", model.AssistantWithCollection).
			Preload("MatchPatterns").
			Preload("States").First(&doc).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			break
		}
		if err != nil {
			fmt.Printf("err occured: %s\n, retry...", err.Error())
			continue
		}
		bk := doc.Assistants
		doc.Assistants = nil
		for _, v := range bk {
			if slices.Contains(assistant, int(v.ID)) {
				doc.Assistants = append(doc.Assistants, v)
			}
		}
		if len(doc.Assistants) != 0 {
			err = model.Transaction(ctx, func(tx *gorm.DB) error {
				return logic.NewDocSyncLogic(ctx, doc, nil).SyncToDBLog(tx)
			})
			if err != nil {
				fmt.Printf("err occured: %s\n, retry", err.Error())
				continue
			}
		}
		latestId = doc.ID
	}
}

// GenDocFileSizeToDB 生成doc文件的大小，存储到数据库中
func GenDocFileSizeToDB() {
	if !config.GetBool("tanlive.cmd.GENDOCFILESIZETODB") {
		return
	}
	defer os.Exit(0)

	ctx := context.TODO()
	limit := 10
	latestId := uint64(0)
	for {
		var rows []*model.TDoc
		err := model.NewQuery[model.TDoc](ctx).Where("data_type = ? and file_size = ? and id > ?", ai.DocType_DOCTYPE_FILE, 0, latestId).
			OrderBy("id", false).
			Limit(limit).Scan(&rows)
		if err != nil {
			fmt.Printf("gen doc file size err: %s\n", err.Error())
			time.Sleep(time.Second * 10)
		}
		if len(rows) == 0 {
			break
		}
		ids := make([]uint64, 0, len(rows))
		for _, row := range rows {
			ids = append(ids, row.ID)
		}
		g := errgroup.Group{}
		for _, v := range rows {
			v := v
			if v.Ref != nil {
				g.Go(func() error {
					size, err := logic.FileSizeCos(v.Ref.Url)
					if err != nil {
						fmt.Printf("gen doc file size err: %s, docId: %d\n", err.Error(), v.ID)
						return err
					}
					err = model.NewQuery[model.TDoc](ctx).DB().Where("id = ?", v.ID).UpdateColumn("file_size", size).Error
					if err != nil {
						fmt.Printf("gen doc file size err: %s, docId: %d\n", err.Error(), v.ID)
					}
					return nil
				})
			}
		}
		g.Wait()
		latestId = rows[len(rows)-1].ID
		fmt.Printf("succeed gen doc file size id: %v\n", ids)
	}
	fmt.Println("gen doc file size done.")
}

// ReparseDoc 重新解析文档
// 不会使用数据已存的cache
// 如果doc有启用状态，会同步向量库
func ReparseDoc() {
	if !config.GetBool("tanlive.cmd.REPARSEDOC") {
		return
	}
	defer os.Exit(0)

	docId := config.GetUint64("tanlive.cmd.REPARSEDOC.DOCID")
	if docId == 0 {
		golog.Println("invalid doc id")
	}

	ctx := context.TODO()
	doc, err := model.NewQuery[model.TDoc](ctx).Where("id = ?", docId).Find()
	if err != nil {
		golog.Fatalf("err occured: %s", err.Error())
	}
	if doc.ID == 0 || doc.DataType != uint32(ai.DocType_DOCTYPE_FILE) {
		return
	}

	f := logic.NewDocFileManager(ctx, doc)
	defer f.Clean()
	err = f.ParseDocToDB(logic.ParseDocParam{
		Force:     true,
		IgnoreErr: true,
	})
	if err != nil {
		golog.Fatalf("err occured: %s", err.Error())
	}
	err = model.NewQuery[model.TDoc](ctx).Where("id = ?", docId).DB().
		Preload("Assistants", model.AssistantWithCollection).
		Preload("MatchPatterns").
		Preload("States").First(&doc).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if err != nil {
		golog.Fatalf("err occured: %s", err.Error())
	}

	// 同步向量
	logic.RemoveDocDisabledCollection(doc)
	logEntry := logic.GenSyncLogEntry(ctx, doc.ID, nil, doc)
	for _, entry := range logEntry {
		err = logic.NewDocSyncLogic(ctx, entry.New, nil).Sync()
		if err != nil {
			golog.Fatalf("err occured: %s", err.Error())
		}
	}
	golog.Println("succeed reparse doc:", doc.ID, ",", doc.FileName)
}

// NormalizeQAHash 生成QA 的 问题去除标点后hash
func NormalizeQAHash() {
	if !config.GetBool("tanlive.cmd.NORMALIZEQAHASH") {
		return
	}
	defer os.Exit(0)

	ctx := context.TODO()
	id := uint64(0)

	var err error
	for {
		var doc *model.TDoc
		err = model.NewQuery[model.TDoc](ctx).Where("id > ? and data_type = ?", id, ai.DocType_DOCTYPE_QA).DB().
			Select("id", "index_text").
			First(&doc).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			golog.Println("完成")
		}
		if err != nil {
			golog.Fatalf("err occured: %s", err.Error())
		}

		if len(doc.IndexText) != 0 {
			model.NewQuery[model.TDoc](ctx).DB().Model(doc).Where("id = ?", doc.ID).
				Select("index_text").Omit("update_date").Updates(doc)
		}
		id = doc.ID
	}
}
