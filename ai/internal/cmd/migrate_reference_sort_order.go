package cmd

import (
	"context"
	"fmt"
	"os"

	"e.coding.net/tencent-ssv/tanlive/gokits/config"
	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"gorm.io/gorm"
)

// MigrateReferenceSortOrder 为参考资料表添加排序字段
func MigrateReferenceSortOrder() {
	if !config.GetBool("tanlive.cmd.MIGRATE_REFERENCE_SORT_ORDER") {
		return
	}
	defer os.Exit(0)
	
	ctx := context.Background()
	db := model.NewQuery[model.TDoc](ctx).DB()
	
	log.WithContext(ctx).Info("开始为参考资料表添加排序字段...")
	
	// 添加 t_doc_reference 表的 sort_order 字段
	if err := addSortOrderToDocReference(db); err != nil {
		log.WithContext(ctx).Errorf("为 t_doc_reference 表添加 sort_order 字段失败: %v", err)
		return
	}
	
	// 添加 t_doc_text_reference 表的 sort_order 字段
	if err := addSortOrderToDocTextReference(db); err != nil {
		log.WithContext(ctx).Errorf("为 t_doc_text_reference 表添加 sort_order 字段失败: %v", err)
		return
	}
	
	// 创建索引
	if err := createSortOrderIndexes(db); err != nil {
		log.WithContext(ctx).Errorf("创建排序索引失败: %v", err)
		return
	}
	
	log.WithContext(ctx).Info("参考资料表排序字段迁移完成")
}

// addSortOrderToDocReference 为 t_doc_reference 表添加 sort_order 字段
func addSortOrderToDocReference(db *gorm.DB) error {
	// 检查字段是否已存在
	var count int64
	err := db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 't_doc_reference' AND column_name = 'sort_order'").Scan(&count).Error
	if err != nil {
		return fmt.Errorf("检查 t_doc_reference.sort_order 字段是否存在失败: %w", err)
	}
	
	if count > 0 {
		log.Info("t_doc_reference.sort_order 字段已存在，跳过添加")
		return nil
	}
	
	// 添加字段
	sql := "ALTER TABLE t_doc_reference ADD COLUMN sort_order INT DEFAULT 0"
	if err := db.Exec(sql).Error; err != nil {
		return fmt.Errorf("添加 t_doc_reference.sort_order 字段失败: %w", err)
	}
	
	log.Info("成功为 t_doc_reference 表添加 sort_order 字段")
	return nil
}

// addSortOrderToDocTextReference 为 t_doc_text_reference 表添加 sort_order 字段
func addSortOrderToDocTextReference(db *gorm.DB) error {
	// 检查字段是否已存在
	var count int64
	err := db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 't_doc_text_reference' AND column_name = 'sort_order'").Scan(&count).Error
	if err != nil {
		return fmt.Errorf("检查 t_doc_text_reference.sort_order 字段是否存在失败: %w", err)
	}
	
	if count > 0 {
		log.Info("t_doc_text_reference.sort_order 字段已存在，跳过添加")
		return nil
	}
	
	// 添加字段
	sql := "ALTER TABLE t_doc_text_reference ADD COLUMN sort_order INT DEFAULT 0"
	if err := db.Exec(sql).Error; err != nil {
		return fmt.Errorf("添加 t_doc_text_reference.sort_order 字段失败: %w", err)
	}
	
	log.Info("成功为 t_doc_text_reference 表添加 sort_order 字段")
	return nil
}

// createSortOrderIndexes 创建排序相关的索引
func createSortOrderIndexes(db *gorm.DB) error {
	indexes := []struct {
		name  string
		table string
		sql   string
	}{
		{
			name:  "idx_doc_reference_sort",
			table: "t_doc_reference",
			sql:   "CREATE INDEX IF NOT EXISTS idx_doc_reference_sort ON t_doc_reference(doc_id, sort_order)",
		},
		{
			name:  "idx_doc_text_reference_sort",
			table: "t_doc_text_reference", 
			sql:   "CREATE INDEX IF NOT EXISTS idx_doc_text_reference_sort ON t_doc_text_reference(doc_id, sort_order)",
		},
	}
	
	for _, idx := range indexes {
		if err := db.Exec(idx.sql).Error; err != nil {
			return fmt.Errorf("创建索引 %s 失败: %w", idx.name, err)
		}
		log.Infof("成功创建索引: %s", idx.name)
	}
	
	return nil
}
