// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"time"

	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/base"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDoc [...]
type TDoc struct {
	ID              uint64            `gorm:"primaryKey;column:id" json:"id,omitempty"`                                 // 自增id
	State           ai.DocState       `gorm:"column:state;default:2" json:"state,omitempty"`                            // 1: 启用 2: 停用 3: 解析中 4: 解析失败 5: 上传文件中 6:解析成功 7: 删除中
	Text            string            `gorm:"column:text" json:"text,omitempty"`                                        // qa: a
	IndexText       string            `gorm:"column:index_text" json:"indexText,omitempty"`                             // qa:q 文本：文本内容
	IndexTextMd5    string            `gorm:"column:index_text_md5" json:"indexTextMd5,omitempty"`                      // md5
	IndexTextCerpt  string            `gorm:"column:index_text_cerpt" json:"indexTextCerpt,omitempty"`                  // 文本片段（前300字符）
	Ref             *DocReference     `gorm:"column:ref;serializer:json" json:"ref,omitempty"`                          // qa:参考文档，文本和文件:文件名称
	FileName        string            `gorm:"column:file_name" json:"fileName,omitempty"`                               // 文本/文件名称
	RagFilename     string            `gorm:"column:rag_filename;default:''" json:"ragFilename,omitempty"`              // rag侧存的文件名称
	UpdateBy        uint64            `gorm:"column:update_by;default:0" json:"updateBy,omitempty"`                     // 最近更新人id
	UpdateByType    base.IdentityType `gorm:"column:update_by_type;default:0" json:"updateByType,omitempty"`            // 更新人类型  1团队 2个人 3运营账户
	UpdateByUser    uint64            `gorm:"column:update_by_user;default:0" json:"updateByUser,omitempty"`            // 更新人个人id，团队用户时取值有效
	UpdateDate      time.Time         `gorm:"column:update_date;default:CURRENT_TIMESTAMP" json:"updateDate,omitempty"` // 最近更新时间
	CreateDate      time.Time         `gorm:"column:create_date;default:CURRENT_TIMESTAMP" json:"createDate,omitempty"` // 创建时间
	CreateBy        uint64            `gorm:"column:create_by;default:0" json:"createBy,omitempty"`                     // 创建人id
	CreateByType    base.IdentityType `gorm:"column:create_by_type;default:0" json:"createByType,omitempty"`            // 创建人类型  1团队 2个人 3运营账户
	CreateByUser    uint64            `gorm:"column:create_by_user;default:0" json:"createByUser,omitempty"`            // 创建人个人id，团队用户时取值有效
	HitCount        uint64            `gorm:"column:hit_count;default:0" json:"hitCount,omitempty"`                     // 命中次数
	UgcType         base.DataType     `gorm:"column:ugc_type;default:0" json:"ugcType,omitempty"`                       // ugc类型
	UgcID           uint64            `gorm:"column:ugc_id;default:0" json:"ugcId,omitempty"`                           // ugc的id
	DataType        uint32            `gorm:"column:data_type;default:0" json:"dataType,omitempty"`                     // 1:qa 2:文本 3:文件
	DeletedAt       gorm.DeletedAt    `gorm:"column:deleted_at" json:"deletedAt,omitempty"`                             // 删除时间
	Version         uint64            `gorm:"column:version" json:"version,omitempty"`                                  // 版本
	RagVersion      uint64            `gorm:"column:rag_version" json:"ragVersion,omitempty"`                           // rag侧的版本
	ShowContributor uint32            `gorm:"column:show_contributor;default:1" json:"showContributor,omitempty"`       // 是否显示贡献者 1:是 2:否(默认为是，帮助中心文档默认为否)
	IsCopy          bool              `gorm:"column:is_copy" json:"isCopy,omitempty"`                                   // 是否为副本：0否 1是
	MainID          uint64            `gorm:"column:main_id" json:"mainId,omitempty"`                                   // 主数据ID
	UgcTitle        string            `gorm:"column:ugc_title" json:"ugcTitle,omitempty"`                               // UGC标题
	DataSource      ai.DocDataSource  `gorm:"column:data_source;default:2" json:"dataSource,omitempty"`                 // 数据来源: 1:ugc 2:知识库 3:腾讯云文档 4:SQL数据
	// IsSystem        bool                    `gorm:"column:is_system" json:"isSystem,omitempty"`                                           // 是否为系数据
	FileSize       int64                   `gorm:"column:file_size" json:"fileSize,omitempty"`                      // 文件大小
	UniqueHash     string                  `gorm:"column:unique_hash" json:"uniqueHash,omitempty"`                  // 判断文件名/qa重复的hash，qa为问题的hash，文件为文件名hash
	NormalizedHash string                  `gorm:"column:normalized_hash" json:"normalizedHash,omitempty"`          // index_text 去掉标点字符后的hash
	ContentState   ai.DocContentState      `gorm:"column:content_state" json:"contentState,omitempty"`              // 内容状态：1同步中、2有更新、3已下架、4已删除、5人工
	DownloadAsRef  ai.DocFileDownloadAsRef `gorm:"column:download_as_ref;default:1" json:"downloadAsRef,omitempty"` // 是否可作为参考资料下载 1是 2否
	Collections    []*TCollection          `gorm:"-:all" json:"collections,omitempty"`                              // doc对应的collection，只有查询后的hook才会填充此值
	References     []*TDoc                 `gorm:"many2many:t_doc_reference;joinForeignKey:doc_id;joinReferences:ref_id" json:"references,omitempty"`
	Assistants     []*TAssistant           `gorm:"->;many2many:t_assistant_doc;joinForeignKey:doc_id;joinReferences:assistant_id" json:"assistants,omitempty"`
	Contributors   []*TDocContributor      `gorm:"foreignKey:doc_id" json:"contributors,omitempty"`
	States         []*TAssistantDoc        `gorm:"foreignKey:doc_id" json:"states,omitempty"`       // 关联表，查询doc在不同助手下的状态
	Copies         []*TDoc                 `gorm:"foreignkey:main_id" json:"copies,omitempty"`      // 副本
	SyncVersions   []*TDocSyncVersion      `gorm:"foreignKey:doc_id" json:"syncVersions,omitempty"` // 向量同步的进度
	Labels         []*TObjectLabel         `gorm:"foreignKey:object_id" json:"labels,omitempty"`
	MatchPatterns  []*TDocMatchPattern     `gorm:"foreignKey:doc_id" json:"matchPatterns,omitempty"`  // 匹配模式
	AssistantDocs  []*TAssistantDoc        `gorm:"foreignKey:doc_id" json:"asistantDoc,omitempty"`    // 助手文档列表
	DocExtend      *TDocExtend             `gorm:"foreignKey:doc_id" json:"docExtend,omitempty"`      // DOC 扩展表
	DocDataSource  *TDocExternalSource     `gorm:"foreignKey:doc_id" json:"docExternal,omitempty"`    // Doc外部数据源信息
	TextReferences []*TDocTextReference    `gorm:"foreignKey:doc_id" json:"textReferences,omitempty"` // 纯文本参考资料
	TableOversize  []*TDocTableOversize    `gorm:"foreignKey:doc_id" json:"tableOversize,omitempty"`  // QA/表格超长信息
	DocShares      []*TDocShare            `gorm:"foreignKey:doc_id" json:"docShares,omitempty"`      // 文档分享信息
}

// TableName get sql table name.获取数据库表名
func (m *TDoc) TableName() string {
	return "t_doc"
}

// TDocColumns get sql column name.获取数据库列名
var TDocColumns = struct {
	ID             string
	State          string
	Contributor    string
	Text           string
	IndexText      string
	IndexTextMd5   string
	Ref            string
	FileName       string
	RagFilename    string
	UpdateBy       string
	UpdateDate     string
	CreateDate     string
	CreateBy       string
	HitCount       string
	UgcType        string
	UgcID          string
	DataType       string
	DataSource     string
	DeletedAt      string
	NormalizedHash string
	UniqueHash     string
}{
	ID:             "id",
	State:          "state",
	Contributor:    "contributor",
	Text:           "text",
	IndexText:      "index_text",
	IndexTextMd5:   "index_text_md5",
	Ref:            "ref",
	FileName:       "file_name",
	RagFilename:    "rag_filename",
	UpdateBy:       "update_by",
	UpdateDate:     "update_date",
	CreateDate:     "create_date",
	CreateBy:       "create_by",
	HitCount:       "hit_count",
	UgcType:        "ugc_type",
	UgcID:          "ugc_id",
	DataType:       "data_type",
	DataSource:     "data_source",
	DeletedAt:      "deleted_at",
	NormalizedHash: "normalized_hash",
	UniqueHash:     "unique_hash",
}
