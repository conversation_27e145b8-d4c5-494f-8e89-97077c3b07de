// Code generated by smartdao. DO NOT EDIT.
// versions:
// smartdao	v1.0.0

package model

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

var (
	_ = time.RFC850
	_ = clause.Associations
	_ = gorm.ScanUpdate
)

// TDocReference [...]
type TDocReference struct {
	DocID int64 `gorm:"primaryKey;column:doc_id" json:"docId"` // doc的id
	RefID int64 `gorm:"primaryKey;column:ref_id" json:"refId"` // 参考资料的id
}

// TableName get sql table name.获取数据库表名
func (m *TDocReference) TableName() string {
	return "t_doc_reference"
}

// TDocReferenceColumns get sql column name.获取数据库列名
var TDocReferenceColumns = struct {
	DocID string
	RefID string
}{
	DocID: "doc_id",
	RefID: "ref_id",
}
