package handler

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"golang.org/x/exp/slices"
	"golang.org/x/sync/errgroup"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// ListQA 获取QA列表
func (a *Ai) ListQA(ctx context.Context, req *ai.ReqListQA, rsp *ai.RspListQA) error {
	var rows []*model.TDoc
	db, tenantScopeAssistantId, err := logic.ListQAApplyFilter(ctx, req)
	if err != nil {
		return err
	}
	var cnt int64
	if err = db.Count(&cnt).Error; err != nil {
		return err
	}
	rsp.Total = uint32(cnt)
	if cnt == 0 {
		return nil
	}

	if req.Page != nil {
		pager := xorm.NewPaginator(req.Page.Offset, req.Page.Limit)
		pager.MaxLimit = 500
		db = (&pager).Apply(db)
	}
	for _, v := range req.OrderBy {
		db.Order(clause.OrderByColumn{Column: clause.Column{Name: v.Column}, Desc: v.Desc})
	}
	if req.OrderByLabel != nil {
		db.Scopes(model.OrderByLabel(ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_QA, req.OrderByLabel))
	}
	if len(req.OrderBy) == 0 && req.OrderByLabel == nil {
		db.Order("update_date desc").Order("id desc")
	}
	if err := db.Find(&rows).Error; err != nil {
		return err
	}
	for _, v := range rows {
		rsp.Items = append(rsp.Items, v.ToQAProto())
	}

	// 获取知识提示
	if req.WithTips {
		// 目前只支持一个贡献者
		var contributor *ai.Contributor
		if len(req.GetTenantCond().GetContributor()) != 0 {
			contributor = req.GetTenantCond().GetContributor()[0].Contributor
		}
		if err := logic.NewDocTipLogic().FillQaAllTipsSummary(ctx, rows, rsp.Items, contributor, tenantScopeAssistantId...); err != nil {
			log.WithContext(ctx).Errorf("FillQaAllTipsSummary failed: %v", err.Error())
			return err
		}
	}
	return nil
}

// CreateQA 创建QA对
func (a *Ai) CreateQA(ctx context.Context, req *ai.ReqCreateQA, rsp *ai.RspCreateQA) error {
	bulkRsp := &ai.RspCreateQAInBulk{}
	err := a.CreateQAInBulk(ctx, &ai.ReqCreateQAInBulk{
		Items:           []*ai.QA{req.Item},
		SharedAssistant: []*ai.ReqCreateQAInBulk_Slice{{Id: req.SharedAssistant}},
	}, bulkRsp)
	if err != nil {
		return err
	}
	rsp.Id = bulkRsp.Id[0]
	return nil
}

// CreateQAInBulk 创建QA对
func (a *Ai) CreateQAInBulk(ctx context.Context, req *ai.ReqCreateQAInBulk, rsp *ai.RspCreateQAInBulk) error {
	var rows []*model.TDoc
	for i, v := range req.Items {
		rows = append(rows, new(model.TDoc).FromQAProto(v))
		rows[i].RagFilename = logic.GenDocRagFileName()
		if len(rows[i].IndexText) != 0 {
			rows[i].IndexTextMd5 = util.CalculateMD5String(rows[i].IndexText)
		}
	}
	if len(rows) == 0 {
		return nil
	}

	err := model.Transaction(ctx, func(tx *gorm.DB) error {
		// 先创建文档主体，但排除关联关系
		if err := tx.Omit(clause.Associations).Create(rows).Error; err != nil {
			return err
		}

		// 为每个创建的QA单独处理参考资料
		for i, v := range rows {
			rsp.Id = append(rsp.Id, v.ID)

			// 如果有参考资料，使用新的方法来保存并保持排序
			if len(req.Items[i].Reference) > 0 {
				if err := v.CreateReferencesWithSort(tx, req.Items[i].Reference); err != nil {
					return err
				}
			}
		}
		return nil
	})
	if err != nil {
		return err
	}

	customAsid := make([][]uint64, 0, len(req.SharedAssistant))
	for _, v := range req.SharedAssistant {
		customAsid = append(customAsid, v.Id)
	}
	xsync.SafeGo(context.Background(), func(ctx context.Context) error {
		by := req.Items[0].CreateBy.Id
		err = logic.ShareDocAfterCreate(ctx, rows[0].CreateByType, by, rsp.Id, customAsid...)
		if err != nil {
			log.WithContext(ctx).Errorf("ShareDocAfterCreate failed. docId: %v, err: %v", rsp.Id, err.Error())
			return err
		}
		// 异步存储超长问题
		logic.StoreOverSizedQuestionAllAssistantBulkAsync(ctx, rsp.Id)
		return nil
	}, boot.TraceGo(ctx))
	return nil
}

// UpdateQAMaskMapping 更新QA时，mask和数据库字段的映射
var UpdateQAMaskMapping = map[string]any{
	"states":           "States",
	"answer":           "text",
	"question":         "index_text",
	"reference":        []string{"References", "Ref"},
	"assistant_id":     "Assistants",
	"update_by":        []string{"update_by", "update_by_type", "update_by_user", "update_date"},
	"contributor":      "contributor",
	"show_contributor": "show_contributor",
	"labels":           "Labels",
	"match_patterns":   "match_patterns",
}

// UpdateQA 更新QA
func (a *Ai) UpdateQA(ctx context.Context, in *ai.ReqUpdateQA, _ *emptypb.Empty) error {
	row := &model.TDoc{
		ID:              in.Id,
		Text:            in.Answer,
		IndexText:       in.Question,
		UpdateBy:        in.GetOperator().GetId(),
		UpdateByType:    in.GetOperator().GetType(),
		UpdateByUser:    in.GetOperator().GetUserId(),
		UpdateDate:      time.Now(),
		HitCount:        uint64(in.HitCount),
		ShowContributor: in.ShowContributor,
	}
	row.FillLabelsFromProto(in.Labels)
	row.FillContributorsFromProto(in.Contributor...)
	row.ReferenceFromProto(in.Reference)
	row.FillStatesFromProto(in.States...)

	old, err := model.LoadDoc(ctx, []uint64{row.ID}, nil)
	if err != nil || len(old) == 0 {
		return err
	}

	// 是否更新了问题
	var questionUpdated bool
	// 是否更新了助手绑定
	var assistantUpdated bool

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		var fields []any
		for _, path := range in.GetMask().GetPaths() {
			if p, ok := UpdateQAMaskMapping[path]; ok {
				fields = append(fields, p)
			}
		}
		if slices.Contains(fields, "index_text") || len(row.IndexText) != 0 {
			questionUpdated = true
			row.IndexTextMd5 = util.CalculateMD5String(row.IndexText)
			if len(fields) != 0 {
				fields = append(fields, "index_text_md5")
			}
		}
		if len(fields) > 0 {
			err = tx.Select(fields[0], fields[1:]...).Omit(clause.Associations).Updates(row).Error
		} else {
			err = tx.Omit(clause.Associations).Updates(row).Error
		}
		if err != nil {
			return err
		}
		if (len(in.States) != 0 && in.Mask == nil) || slices.Contains(fields, "States") {
			assistantUpdated = true
			err := model.ReplaceDocAssistants(tx, []uint64{row.ID}, in.ScopedAssistantId, row.States)
			if err != nil {
				return err
			}
		}
		if (len(in.Reference) != 0 && in.Mask == nil) || slices.Contains(in.GetMask().GetPaths(), "reference") {
			// 使用新的方法来替换参考资料并保持排序
			err = row.ReplaceReferencesWithSort(tx, in.Reference)
			if err != nil {
				return err
			}
		}
		if (len(in.Contributor) != 0 && in.Mask == nil) || slices.Contains(fields, "contributor") {
			err = tx.Model(row).Association("Contributors").Unscoped().Replace(row.Contributors)
			if err != nil {
				return err
			}
		}
		if (len(in.Labels) != 0 && in.Mask == nil) || slices.Contains(fields, "Labels") {
			// 替换原有的标签关系
			err = logic.ReplaceObjectLabels(tx, row.ID, in.LabelTenant, ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_QA, row.Labels)
			if err != nil {
				return err
			}
		}
		if (len(in.MatchPatterns) != 0 && in.Mask == nil) || slices.Contains(fields, "match_patterns") {
			matchPatterns := make([]*model.TDocMatchPattern, 0, len(in.MatchPatterns))
			for _, m := range in.MatchPatterns {
				matchPatterns = append(matchPatterns, &model.TDocMatchPattern{
					MatchPattern: m,
				})
			}
			err = tx.Model(row).Association("MatchPatterns").Unscoped().Replace(matchPatterns)
			if err != nil {
				return err
			}
		}

		if err = logic.UpdateRagData(ctx, tx, row, old[0]); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	// 如果更新了问题或助手绑定，则异步计算超长问题并存数据库
	if questionUpdated || assistantUpdated {
		logic.StoreOverSizedQuestionAllAssistantBulkAsync(ctx, []uint64{row.ID})
	}
	return nil
}

// UpdateQAInBulk 批量更新QA
func (a *Ai) UpdateQAInBulk(ctx context.Context, in *ai.ReqUpdateQAInBulk, _ *emptypb.Empty) error {
	errg, ctx := errgroup.WithContext(ctx)
	for _, v := range in.Items {
		v := v
		errg.Go(func() error {
			err := a.UpdateQA(ctx, v, nil)
			if err != nil {
				return err
			}
			return nil
		})
	}
	err := errg.Wait()
	if err != nil {
		return err
	}
	return nil
}

// GetQaTip 查询QA的知识提示（问题超长）等信息
func (a *Ai) GetQaTip(ctx context.Context, req *ai.ReqGetQaTip, rsp *ai.RspGetQaTip) error {
	// 获取问题超长提示
	qs, err := logic.NewDocTipLogic().GetQaAllTips(ctx, req.GetId(), req.GetContributor(), req.GetAssistantId()...)
	if err != nil {
		return err
	}

	rsp.QuestionOverSize = qs.QuestionOverSize
	rsp.Repeated = qs.Repeated
	return nil
}
