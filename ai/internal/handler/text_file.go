package handler

import (
	"context"
	"time"

	"e.coding.net/tencent-ssv/tanlive/gokits/log"
	"e.coding.net/tencent-ssv/tanlive/gokits/xorm"
	"e.coding.net/tencent-ssv/tanlive/gokits/xsync"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic"
	tablechunk "e.coding.net/tencent-ssv/tanlive/services/ai/internal/logic/docchunk/table"
	"e.coding.net/tencent-ssv/tanlive/services/ai/internal/model"
	"e.coding.net/tencent-ssv/tanlive/services/ai/pkg/rag"
	"e.coding.net/tencent-ssv/tanlive/services/ai/util"
	"e.coding.net/tencent-ssv/tanlive/services/pkg/boot"
	"e.coding.net/tencent-ssv/tanlive/services/proto/tanlive/ai"
	"golang.org/x/exp/slices"
	"golang.org/x/sync/errgroup"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// ListTextFile 搜索文本和文件
func (a *Ai) ListTextFile(ctx context.Context, req *ai.ReqListTextFile, rsp *ai.RspListTextFile) error {
	db, tenantScopeAssistantId, err := logic.ListTextFileApplyFilter(ctx, req)
	if err != nil {
		return err
	}

	var cnt int64
	if err := db.Count(&cnt).Error; err != nil {
		return err
	}
	rsp.Total = uint32(cnt)
	if cnt == 0 {
		return nil
	}

	var rows []*model.TDoc
	if req.Page != nil {
		pager := xorm.NewPaginator(req.Page.Offset, req.Page.Limit)
		pager.MaxLimit = 500
		db = (&pager).Apply(db)
	}
	for _, v := range req.OrderBy {
		db.Order(clause.OrderByColumn{Column: clause.Column{Name: v.Column}, Desc: v.Desc})
	}
	if req.OrderByLabel != nil {
		labelObjType := model.DocSource2CustomLabelObjectType(req.DataSource)
		db.Scopes(model.OrderByLabel(labelObjType, req.OrderByLabel))
	}
	if len(req.OrderBy) == 0 && req.OrderByLabel == nil {
		db.Order("update_date desc").Order("id desc")
	}
	if err := db.Find(&rows).Error; err != nil {
		return err
	}

	rsp.Items = model.TDocs(rows).ToFullTextFilePb()

	// 获取解析进度
	for i, v := range rows {
		if v.State == ai.DocState_DOC_STATE_PARSING || v.State == ai.DocState_DOC_STATE_REPARSING {
			progress, err := logic.NewDocFileManager(ctx, v).GetOcrParseProgress()
			if err != nil {
				return err
			}
			rsp.Items[i].Doc.ParseProgress = progress
		}
	}

	// 获取知识提示
	if req.WithTips {
		// 目前只支持一个贡献者
		var contributor *ai.Contributor
		if len(req.GetTenantCond().GetContributor()) != 0 {
			contributor = req.GetTenantCond().GetContributor()[0].Contributor
		}
		if err := logic.NewDocTipLogic().FillTextFileAllTipsSummary(ctx, rows, rsp.Items, contributor, tenantScopeAssistantId...); err != nil {
			log.WithContext(ctx).Errorf("checkOverSizedTables failed: %v", err.Error())
			return err
		}
	}

	count, err := model.NewQuery[model.TDoc](ctx).Where("state = ?", ai.DocState_DOC_STATE_PARES_FAILED).Count()
	rsp.FailParseCount = count
	return err
}

// CreateTextFileInBulk 创建textfile
func (a *Ai) CreateTextFileInBulk(ctx context.Context, req *ai.ReqCreateTextFileInBulk, rsp *ai.RspCreateTextFileInBulk) error {
	var rows []*model.TDoc
	for _, v := range req.Items {
		rows = append(rows, new(model.TDoc).FromTextFileProto(v))
	}
	for _, v := range rows {
		v.RagFilename = logic.GenDocRagFileName()
		v.DataType = v.GetDocType()
		// 文件设为解析中状态,如果已经上传了文件到cos
		if v.DataType == uint32(ai.DocType_DOCTYPE_FILE) {
			if v.Ref.Url != "" {
				v.State = ai.DocState_DOC_STATE_PARSING
			} else {
				v.State = ai.DocState_DOC_STATE_FILE_UPLOADING
			}
		}
		if v.GetDocType() == uint32(ai.DocType_DOCTYPE_TEXT) && v.IndexText != "" {
			v.IndexText = rag.MergeLines(v.IndexText)
			v.IndexTextMd5 = util.CalculateMD5String(v.IndexText)
		}
	}
	// 先创建文档主体，但排除关联关系和DocExtend
	db := model.NewQuery[model.TDoc](ctx).DB().Omit("DocExtend").Omit(clause.Associations)
	if err := db.Create(rows).Error; err != nil {
		return err
	}

	// 为每个创建的文本文件单独处理参考资料
	for i, v := range rows {
		// 如果有参考资料，使用新的方法来保存并保持排序
		if len(req.Items[i].Reference) > 0 {
			if err := v.CreateReferencesWithSort(db, req.Items[i].Reference); err != nil {
				return err
			}
		}
	}
	var extendRows []*model.TDocExtend
	for _, v := range rows {
		rsp.Id = append(rsp.Id, v.ID)
		v.DocExtend.DocID = v.ID
		if v.DocExtend.ParseMode != ai.DocParseMode_DOC_PARSE_MODE_UNSPECIFIED {
			extendRows = append(extendRows, v.DocExtend)
		}
	}
	if len(extendRows) > 0 {
		if err := model.NewQuery[model.TDocExtend](ctx).Insert(extendRows); err != nil {
			log.WithContext(ctx).Errorw("CreateTextFileInBulk create extend failed", "err", err)
		}
	}

	customAsid := make([][]uint64, 0, len(req.SharedAssistant))
	for _, v := range req.SharedAssistant {
		customAsid = append(customAsid, v.Id)
	}
	for _, v := range rows {
		v := v
		if v.DataType == uint32(ai.DocType_DOCTYPE_FILE) && v.State == ai.DocState_DOC_STATE_PARSING {
			xsync.SafeGo(context.Background(), func(ctx context.Context) error {
				log.WithContext(ctx).Info("share and parse doc after create", "docId", v.ID)
				by := req.Items[0].CreateBy.Id
				err := logic.ShareDocAfterCreate(ctx, rows[0].CreateByType, by, []uint64{v.ID}, customAsid...)
				if err != nil {
					log.WithContext(ctx).Errorf("ShareDocAfterCreate failed. docId: %v, err: %v", v.ID, err.Error())
					return err
				}
				// 先创建好分享关系，再解析
				logic.NewParseDocLogic().StartParse(ctx, v)
				return nil
			}, boot.TraceGo(ctx))
		}
		if v.DataType == uint32(ai.DocType_DOCTYPE_TEXT) {
			xsync.SafeGo(context.Background(), func(ctx context.Context) error {
				log.WithContext(ctx).Info("share and parse doc after create", "docId", v.ID)
				by := req.Items[0].CreateBy.Id
				err := logic.ShareDocAfterCreate(ctx, rows[0].CreateByType, by, []uint64{v.ID}, customAsid...)
				if err != nil {
					log.WithContext(ctx).Errorf("ShareDocAfterCreate failed. docId: %v, err: %v", v.ID, err.Error())
					return err
				}

				// 处理文档表格信息
				docTableLogic := tablechunk.NewDocTableLogic(model.NewConnection(ctx))
				if err := docTableLogic.StoreOversizedTablesAllAssistant(ctx, v.ID); err != nil {
					log.WithContext(ctx).Errorf("StoreOversizedTablesAllAssistant failed. docId: %v, err: %v", v.ID, err.Error())
					return err
				}
				return nil
			}, boot.TraceGo(ctx))
		}
	}

	return nil
}

// UpdateFileMaskMapping 更新文本/文件时，mask和数据库字段的映射
var UpdateFileMaskMapping = map[string]any{
	"states":           "States",
	"text":             "index_text",
	"assistant_id":     "Assistants",
	"url":              "ref",
	"parsed_url":       "ref",
	"ugc_type":         "ugc_type",
	"ugc_id":           "ugc_id",
	"name":             "file_name",
	"contributor":      "contributor",
	"update_by":        []string{"update_by", "update_by_type", "update_by_user", "update_date"},
	"show_contributor": "show_contributor",
	"labels":           "Labels",
	"reference":        []string{"References", "Ref"},
	"download_as_ref":  "download_as_ref",
}

// UpdateTextFile 更新文本或文件内容
func (a *Ai) UpdateTextFile(ctx context.Context, req *ai.ReqUpdateTextFile, _ *emptypb.Empty) error {
	// 从v2.10开始该接口不再支持更新文本，更新文本需使用 ChunkDoc 接口
	req.Text = ""
	if req.GetMask().GetPaths() != nil {
		req.Mask.Paths = slices.DeleteFunc(req.Mask.Paths, func(s string) bool {
			return s == "text"
		})
	}

	old, err := model.LoadDoc(ctx, []uint64{req.Id}, nil)
	if err != nil || len(old) == 0 {
		return err
	}
	row := &model.TDoc{
		ID:              req.Id,
		UpdateBy:        req.GetUpdateBy().GetId(),
		UpdateByType:    req.GetUpdateBy().GetType(),
		UpdateByUser:    req.GetUpdateBy().GetUserId(),
		UpdateDate:      time.Now(),
		IndexText:       req.Text,
		HitCount:        uint64(req.HitCount),
		UgcID:           req.UgcId,
		UgcType:         req.UgcType,
		FileName:        req.Name,
		ShowContributor: req.ShowContributor,
		DownloadAsRef:   req.DownloadAsRef,
		Ref:             old[0].Ref,
	}
	row.FillLabelsFromProto(req.Labels)
	row.FillStatesFromProto(req.States...)
	row.FillContributorsFromProto(req.Contributor...)
	// row.FillAssistantsFromId(req.AssistantId...)
	var fields []any
	for _, path := range req.GetMask().GetPaths() {
		if p, ok := UpdateFileMaskMapping[path]; ok {
			fields = append(fields, p)
		}
	}
	if req.Url != "" || req.ParsedUrl != "" {
		row.Ref = &model.DocReference{
			Name:      req.Name,
			Url:       req.Url,
			ParsedUrl: req.ParsedUrl,
		}
		if row.Ref.Url != old[0].Ref.Url || row.Ref.ParsedUrl != old[0].Ref.ParsedUrl {
			row.State = ai.DocState_DOC_STATE_REPARSING
			if len(fields) != 0 {
				fields = append(fields, "state")
			}
		}
	}
	row.ReferenceFromProto(req.Reference)
	// 用户手动编辑了文件内容
	if old[0].State == ai.DocState_DOC_STATE_PARES_FAILED && len(row.IndexText) != 0 && row.State == 0 {
		row.State = ai.DocState_DOC_STATE_DISABLED
		if len(fields) != 0 {
			fields = append(fields, "state")
		}
	}
	if slices.Contains(fields, "index_text") || len(row.IndexText) != 0 {
		row.IndexTextMd5 = util.CalculateMD5String(row.IndexText)
		if len(fields) != 0 {
			fields = append(fields, "index_text_md5")
		}
	}

	err = model.Transaction(ctx, func(tx *gorm.DB) error {
		if len(fields) > 0 {
			err = tx.Select(fields[0], fields[1:]...).Omit(clause.Associations).Updates(row).Error
		} else {
			err = tx.Omit(clause.Associations).Updates(row).Error
		}
		if err != nil {
			return err
		}
		if (len(req.States) != 0 && req.Mask == nil) || slices.Contains(fields, "States") {
			if err = model.ReplaceDocAssistants(tx, []uint64{req.Id}, req.ScopedAssistantId, row.States); err != nil {
				return err
			}
		}
		if (len(req.Reference) != 0 && req.Mask == nil) || slices.Contains(req.GetMask().GetPaths(), "reference") {
			// 使用新的方法来替换参考资料并保持排序
			err = row.ReplaceReferencesWithSort(tx, req.Reference)
			if err != nil {
				return err
			}
		}
		if (len(req.Contributor) != 0 && req.Mask == nil) || slices.Contains(fields, "contributor") {
			err = tx.Model(row).Association("Contributors").Unscoped().Replace(row.Contributors)
			if err != nil {
				return err
			}
		}
		if (len(req.Labels) != 0 && req.Mask == nil) || slices.Contains(fields, "Labels") {
			// 替换原有的标签关系
			err = logic.ReplaceObjectLabels(tx, row.ID, req.LabelTenant, ai.CustomLabelObjectType_CUSTOM_LABEL_OBJECT_TYPE_FILE, row.Labels)
			if err != nil {
				return err
			}
		}
		if err = logic.UpdateRagData(ctx, tx, row, old[0]); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}

	// 更新了文件链接，需要重新解析
	if row.State == ai.DocState_DOC_STATE_REPARSING {
		xsync.SafeGo(context.Background(), func(ctx context.Context) error {
			log.WithContext(ctx).Info("reparse doc after update url", "docId", row.ID)
			news, err := model.LoadDoc(ctx, []uint64{row.ID}, nil)
			if err != nil {
				return nil
			}
			if len(news) == 0 {
				return nil
			}
			logic.NewReparseDocLogic().StartParse(ctx, news[0])
			return nil
		}, boot.TraceGo(ctx))
	}

	// 更新了助手，更新表格标志信息
	if (len(req.States) != 0 && req.Mask == nil) || slices.Contains(fields, "States") {
		xsync.Go(context.Background(), func(ctx context.Context) error {
			_ = tablechunk.NewDocTableLogic(model.NewConnection(ctx)).StoreOversizedTablesAllAssistant(ctx, row.ID)
			return nil
		}, boot.TraceGo(ctx))
	}

	return nil
}

// UpdateTextFileInBulk 更新文本或文件内容
func (a *Ai) UpdateTextFileInBulk(ctx context.Context, req *ai.ReqUpdateTextFileInBulk, _ *emptypb.Empty) error {
	errg, ctx := errgroup.WithContext(ctx)
	for _, v := range req.Items {
		v := v
		errg.Go(func() error {
			err := a.UpdateTextFile(ctx, v, nil)
			if err != nil {
				return err
			}
			return nil
		})
	}
	err := errg.Wait()
	if err != nil {
		return err
	}
	return nil
}

// ReparseTextFiles 重新解析文件
func (a *Ai) ReparseTextFiles(ctx context.Context, req *ai.ReqReparseTextFiles, rsp *ai.RspReparseTextFiles) error {
	if req.QueryId > 0 {
		asyncTask, err := logic.CreateReparseBatchTask(ctx, req)
		rsp.Async = asyncTask
		return err
	}
	return logic.ReparseDocInBulk(ctx, req)
}

// GetTextFileTip  查询文本文件的知识提示（解析失败，文件名重复，表头过长）等信息
func (a *Ai) GetTextFileTip(ctx context.Context, req *ai.ReqGetTextFileTip, rsp *ai.RspGetTextFileTip) error {
	tip, err := logic.NewDocTipLogic().GetTextFileAllTips(ctx, req.GetId(), req.GetContributor(), req.GetAssistantId()...)
	if err != nil {
		return err
	}
	rsp.Repeated = tip.Repeated
	rsp.State = tip.State
	rsp.TableOverSize = tip.TableOverSize
	return nil
}
